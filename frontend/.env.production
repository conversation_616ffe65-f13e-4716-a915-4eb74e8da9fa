# Production Environment Configuration
# This file is used when deploying the frontend to production

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_test_c2V0LXdvbGYtOTguY2xlcmsuYWNjb3VudHMuZGV2JA

# Supabase Configuration
VITE_SUPABASE_URL=https://zzyoexvzuoezdxuxixns.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ZUIMpo-qSLtovRwJU7fn0f7G2ldF3hO4ST2y96hMrV8

# API Configuration - Production with Nginx Proxy
# All API calls go through Nginx proxy (no direct backend access)
# Use empty string so frontend adds full paths like /api/v1/health
VITE_API_BASE_URL=
VITE_API_BASE_URL_BACKUP=
VITE_API_BASE_URL_FALLBACK=
VITE_WS_URL=/ws

# Session Timeout Configuration (in milliseconds)
VITE_SESSION_TIMEOUT_MS=1800000
VITE_SESSION_WARNING_MS=300000

# Environment
VITE_NODE_ENV=production

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_LOGS=false
VITE_ENABLE_VOICE=true

# Debug
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
