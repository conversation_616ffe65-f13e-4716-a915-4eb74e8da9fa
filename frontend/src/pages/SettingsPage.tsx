import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../shared/components/ui';

export const SettingsPage: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-2"
      >
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Customize your experience and manage your account.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Coming Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Settings panel with theme customization, avatar preferences, 
              notification settings, and account management will be available soon.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};
