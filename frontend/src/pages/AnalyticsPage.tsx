import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../shared/components/ui';

export const AnalyticsPage: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-2"
      >
        <h1 className="text-3xl font-bold">Analytics</h1>
        <p className="text-muted-foreground">
          Track your spiritual journey and conversation insights.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Coming Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Analytics dashboard with conversation insights, spiritual growth tracking, 
              and personalized recommendations will be available soon.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};
