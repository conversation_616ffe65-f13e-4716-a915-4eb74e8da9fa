# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
https://zzyoexvzuoezdxuxixns.supabase.co
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# API Configuration
VITE_API_BASE_URL=https://gitagpt-backend-latest.onrender.com
VITE_API_BASE_URL_BACKUP=https://gitagpt-backend-latest.onrender.com
VITE_API_BASE_URL_FALLBACK=https://gitagpt-backend-latest.onrender.com

# Session Timeout Configuration (in milliseconds)
# Default: 30 minutes (1800000ms) for session timeout
VITE_SESSION_TIMEOUT_MS=1800000

# Default: 5 minutes (300000ms) for warning before timeout
VITE_SESSION_WARNING_MS=300000
VITE_WS_URL=wss://gitagpt-backend-latest.onrender.com

# Environment
VITE_NODE_ENV=development

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_LOGS=true
VITE_ENABLE_VOICE=true

# Session Management
VITE_SESSION_TIMEOUT_MS=1800000
VITE_SESSION_WARNING_MS=300000

# Debug
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
