import{r as e,j as t,R as n}from"./three-vendor-BoFN0Tam.js";import{a as r,g as o}from"./react-vendor-CciLe7Wb.js";var i=r();const a=o(i);function c(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let n=!1;const r=e.map(e=>{const r=u(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():u(e[t],null)}}}}function l(...t){return e.useCallback(s(...t),t)}function d(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const o=r.reduce((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}var f=globalThis?.document?e.useLayoutEffect:()=>{},p=n[" useId ".trim().toString()]||(()=>{}),m=0;function v(t){const[n,r]=e.useState(p());return f(()=>{r(e=>e??String(m++))},[t]),t||(n?`radix-${n}`:"")}var h=n[" useInsertionEffect ".trim().toString()]||f;function g({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,a,c]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),a=e.useRef(n);return h(()=>{a.current=n},[n]),e.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,o,a]}({defaultProp:n,onChange:r}),u=void 0!==t,s=u?t:i;{const n=e.useRef(void 0!==t);e.useEffect(()=>{const e=n.current;if(e!==u){}n.current=u},[u,o])}const l=e.useCallback(e=>{if(u){const n=function(e){return"function"==typeof e}(e)?e(t):e;n!==t&&c.current?.(n)}else a(e)},[u,t,a,c]);return[s,l]}function y(n){const r=E(n),o=e.forwardRef((n,o)=>{const{children:i,...a}=n,c=e.Children.toArray(i),u=c.find(w);if(u){const n=u.props.children,i=c.map(t=>t===u?e.Children.count(n)>1?e.Children.only(null):e.isValidElement(n)?n.props.children:null:t);return t.jsx(r,{...a,ref:o,children:e.isValidElement(n)?e.cloneElement(n,void 0,i):null})}return t.jsx(r,{...a,ref:o,children:i})});return o.displayName=`${n}.Slot`,o}function E(t){const n=e.forwardRef((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?s(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null});return n.displayName=`${t}.SlotClone`,n}var b=Symbol("radix.slottable");function w(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===b}var C=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((n,r)=>{const o=y(`Primitive.${r}`),i=e.forwardRef((e,n)=>{const{asChild:i,...a}=e,c=i?o:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),t.jsx(c,{...a,ref:n})});return i.displayName=`Primitive.${r}`,{...n,[r]:i}},{});function N(t){const n=e.useRef(t);return e.useEffect(()=>{n.current=t}),e.useMemo(()=>(...e)=>n.current?.(...e),[])}var R,O="dismissableLayer.update",D="dismissableLayer.pointerDownOutside",x="dismissableLayer.focusOutside",S=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),P=e.forwardRef((n,r)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:u,onInteractOutside:s,onDismiss:d,...f}=n,p=e.useContext(S),[m,v]=e.useState(null),h=m?.ownerDocument??globalThis?.document,[,g]=e.useState({}),y=l(r,e=>v(e)),E=Array.from(p.layers),[b]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),w=E.indexOf(b),P=m?E.indexOf(m):-1,_=p.layersWithOutsidePointerEventsDisabled.size>0,A=P>=w,L=function(t,n=globalThis?.document){const r=N(t),o=e.useRef(!1),i=e.useRef(()=>{});return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){let t=function(){T(D,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{const t=e.target,n=[...p.branches].some(e=>e.contains(t));A&&!n&&(a?.(e),s?.(e),e.defaultPrevented||d?.())},h),I=function(t,n=globalThis?.document){const r=N(t),o=e.useRef(!1);return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){T(x,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{const t=e.target;[...p.branches].some(e=>e.contains(t))||(u?.(e),s?.(e),e.defaultPrevented||d?.())},h);return function(t,n=globalThis?.document){const r=N(t);e.useEffect(()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})},[r,n])}(e=>{P===p.layers.size-1&&(i?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},h),e.useEffect(()=>{if(m)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(R=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(m)),p.layers.add(m),M(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=R)}},[m,h,o,p]),e.useEffect(()=>()=>{m&&(p.layers.delete(m),p.layersWithOutsidePointerEventsDisabled.delete(m),M())},[m,p]),e.useEffect(()=>{const e=()=>g({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)},[]),t.jsx(C.div,{...f,ref:y,style:{pointerEvents:_?A?"auto":"none":void 0,...n.style},onFocusCapture:c(n.onFocusCapture,I.onFocusCapture),onBlurCapture:c(n.onBlurCapture,I.onBlurCapture),onPointerDownCapture:c(n.onPointerDownCapture,L.onPointerDownCapture)})});P.displayName="DismissableLayer";function M(){const e=new CustomEvent(O);document.dispatchEvent(e)}function T(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?function(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}(o,a):o.dispatchEvent(a)}e.forwardRef((n,r)=>{const o=e.useContext(S),i=e.useRef(null),a=l(r,i);return e.useEffect(()=>{const e=i.current;if(e)return o.branches.add(e),()=>{o.branches.delete(e)}},[o.branches]),t.jsx(C.div,{...n,ref:a})}).displayName="DismissableLayerBranch";var _="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},I=e.forwardRef((n,r)=>{const{loop:o=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...u}=n,[s,d]=e.useState(null),f=N(a),p=N(c),m=e.useRef(null),v=l(r,e=>d(e)),h=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect(()=>{if(i){let e=function(e){if(h.paused||!s)return;const t=e.target;s.contains(t)?m.current=t:W(m.current,{select:!0})},t=function(e){if(h.paused||!s)return;const t=e.relatedTarget;null!==t&&(s.contains(t)||W(m.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&W(s)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[i,s,h.paused]),e.useEffect(()=>{if(s){B.add(h);const t=document.activeElement;if(!s.contains(t)){const n=new CustomEvent(_,L);s.addEventListener(_,f),s.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(W(r,{select:t}),document.activeElement!==n)return}((e=j(s),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===t&&W(s))}return()=>{s.removeEventListener(_,f),setTimeout(()=>{const e=new CustomEvent(A,L);s.addEventListener(A,p),s.dispatchEvent(e),e.defaultPrevented||W(t??document.body,{select:!0}),s.removeEventListener(A,p),B.remove(h)},0)}}var e},[s,f,p,h]);const g=e.useCallback(e=>{if(!o&&!i)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[r,i]=function(e){const t=j(e),n=k(t,e),r=k(t.reverse(),e);return[n,r]}(t);r&&i?e.shiftKey||n!==i?e.shiftKey&&n===r&&(e.preventDefault(),o&&W(i,{select:!0})):(e.preventDefault(),o&&W(r,{select:!0})):n===t&&e.preventDefault()}},[o,i,h.paused]);return t.jsx(C.div,{tabIndex:-1,...u,ref:v,onKeyDown:g})});function j(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function k(e,t){for(const n of e)if(!F(n,{upTo:t}))return n}function F(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function W(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}I.displayName="FocusScope";var B=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=$(e,t),e.unshift(t)},remove(t){e=$(e,t),e[0]?.resume()}}}();function $(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var U=e.forwardRef((n,r)=>{const{container:o,...i}=n,[c,u]=e.useState(!1);f(()=>u(!0),[]);const s=o||c&&globalThis?.document?.body;return s?a.createPortal(t.jsx(C.div,{...i,ref:r}),s):null});U.displayName="Portal";var K=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),a=e.useRef("none"),c=t?"mounted":"unmounted",[u,s]=function(t,n){return e.useReducer((e,t)=>n[e][t]??e,t)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=X(o.current);a.current="mounted"===u?e:"none"},[u]),f(()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=X(e);if(t)s("MOUNT");else if("none"===o||"none"===e?.display)s("UNMOUNT");else{s(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}},[t,s]),f(()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=X(o.current).includes(r.animationName);if(r.target===n&&a&&(s("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},c=e=>{e.target===n&&(a.current=X(o.current))};return n.addEventListener("animationstart",c),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",c),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}s("ANIMATION_END")},[n,s]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:e.useCallback(e=>{o.current=e?getComputedStyle(e):null,r(e)},[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=l(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function X(e){return e?.animationName||"none"}K.displayName="Presence";var Y=0;function z(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var V=function(){return V=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},V.apply(this,arguments)};function Z(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var H="right-scroll-bar-position",q="width-before-scroll-bar";function G(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var J="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,Q=new WeakMap;function ee(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach(function(t){return G(t,e)})},(i=e.useState(function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=o,i.facade);return J(function(){var e=Q.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach(function(e){r.has(e)||G(e,null)}),r.forEach(function(e){n.has(e)||G(e,o)})}Q.set(a,t)},[t]),a}function te(e){return e}var ne=function(t){var n=t.sideCar,r=Z(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,V({},r))};ne.isSideCarExport=!0;var re=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=te);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=V({async:!0,ssr:!1},e),t}(),oe=function(){},ie=e.forwardRef(function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:oe,onWheelCapture:oe,onTouchMoveCapture:oe}),i=o[0],a=o[1],c=t.forwardProps,u=t.children,s=t.className,l=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noRelative,v=t.noIsolation,h=t.inert,g=t.allowPinchZoom,y=t.as,E=void 0===y?"div":y,b=t.gapMode,w=Z(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=p,N=ee([r,n]),R=V(V({},w),i);return e.createElement(e.Fragment,null,d&&e.createElement(C,{sideCar:re,removeScrollBar:l,shards:f,noRelative:m,noIsolation:v,inert:h,setCallbacks:a,allowPinchZoom:!!g,lockRef:r,gapMode:b}),c?e.cloneElement(e.Children.only(u),V(V({},R),{ref:N})):e.createElement(E,V({},R,{className:s,ref:N}),u))});ie.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ie.classNames={fullWidth:q,zeroRight:H};function ae(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var ce=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=ae())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ue=function(){var t,n=(t=ce(),function(n,r){e.useEffect(function(){return t.add(n),function(){t.remove()}},[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},se={left:0,top:0,right:0,gap:0},le=function(e){return parseInt(e||"",10)||0},de=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return se;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[le(n),le(r),le(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},fe=ue(),pe="data-scroll-locked",me=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(pe,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(H," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(q," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(H," .").concat(H," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(q," .").concat(q," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(pe,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},ve=function(){var e=parseInt(document.body.getAttribute(pe)||"0",10);return isFinite(e)?e:0},he=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect(function(){return document.body.setAttribute(pe,(ve()+1).toString()),function(){var e=ve()-1;e<=0?document.body.removeAttribute(pe):document.body.setAttribute(pe,e.toString())}},[]);var a=e.useMemo(function(){return de(i)},[i]);return e.createElement(fe,{styles:me(a,!n,i,r?"":"!important")})},ge=!1;if("undefined"!=typeof window)try{var ye=Object.defineProperty({},"passive",{get:function(){return ge=!0,!0}});window.addEventListener("test",ye,ye),window.removeEventListener("test",ye,ye)}catch(Dt){ge=!1}var Ee=!!ge&&{passive:!1},be=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},we=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Ce(e,r)){var o=Ne(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Ce=function(e,t){return"v"===e?function(e){return be(e,"overflowY")}(t):function(e){return be(e,"overflowX")}(t)},Ne=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Re=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Oe=function(e){return[e.deltaX,e.deltaY]},De=function(e){return e&&"current"in e?e.current:e},xe=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Se=0,Pe=[];function Me(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Te=(_e=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(Se++)[0],a=e.useState(ue)[0],c=e.useRef(t);e.useEffect(function(){c.current=t},[t]),e.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(De),!0).filter(Boolean);return e.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[t.inert,t.lockRef.current,t.shards]);var u=e.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var n,i=Re(e),a=r.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],l=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===l.type)return!1;var f=we(d,l);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=we(d,l)),!f)return!1;if(!o.current&&"changedTouches"in e&&(u||s)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,c=t.contains(a),u=!1,s=i>0,l=0,d=0;do{if(!a)break;var f=Ne(e,a),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&Ce(e,a)&&(l+=m,d+=p);var v=a.parentNode;a=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(s&&Math.abs(l)<1||!s&&Math.abs(d)<1)&&(u=!0),u}(p,t,e,"h"===p?u:s)},[]),s=e.useCallback(function(e){var t=e;if(Pe.length&&Pe[Pe.length-1]===a){var r="deltaY"in t?Oe(t):Re(t),o=n.current.filter(function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o})[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(c.current.shards||[]).map(De).filter(Boolean).filter(function(e){return e.contains(t.target)});(i.length>0?u(t,i[0]):!c.current.noIsolation)&&t.cancelable&&t.preventDefault()}}},[]),l=e.useCallback(function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:Me(r)};n.current.push(i),setTimeout(function(){n.current=n.current.filter(function(e){return e!==i})},1)},[]),d=e.useCallback(function(e){r.current=Re(e),o.current=void 0},[]),f=e.useCallback(function(e){l(e.type,Oe(e),e.target,u(e,t.lockRef.current))},[]),p=e.useCallback(function(e){l(e.type,Re(e),e.target,u(e,t.lockRef.current))},[]);e.useEffect(function(){return Pe.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,Ee),document.addEventListener("touchmove",s,Ee),document.addEventListener("touchstart",d,Ee),function(){Pe=Pe.filter(function(e){return e!==a}),document.removeEventListener("wheel",s,Ee),document.removeEventListener("touchmove",s,Ee),document.removeEventListener("touchstart",d,Ee)}},[]);var m=t.removeScrollBar,v=t.inert;return e.createElement(e.Fragment,null,v?e.createElement(a,{styles:xe(i)}):null,m?e.createElement(he,{noRelative:t.noRelative,gapMode:t.gapMode}):null)},re.useMedium(_e),ne);var _e,Ae=e.forwardRef(function(t,n){return e.createElement(ie,V({},t,{ref:n,sideCar:Te}))});Ae.classNames=ie.classNames;var Le=new WeakMap,Ie=new WeakMap,je={},ke=0,Fe=function(e){return e&&(e.host||Fe(e.parentNode))},We=function(e,t,n,r){var o=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=Fe(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);je[n]||(je[n]=new WeakMap);var i=je[n],a=[],c=new Set,u=new Set(o),s=function(e){e&&!c.has(e)&&(c.add(e),s(e.parentNode))};o.forEach(s);var l=function(e){e&&!u.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(c.has(e))l(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(Le.get(e)||0)+1,s=(i.get(e)||0)+1;Le.set(e,u),i.set(e,s),a.push(e),1===u&&o&&Ie.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}})};return l(t),c.clear(),ke++,function(){a.forEach(function(e){var t=Le.get(e)-1,o=i.get(e)-1;Le.set(e,t),i.set(e,o),t||(Ie.has(e)||e.removeAttribute(r),Ie.delete(e)),o||e.removeAttribute(n)}),--ke||(Le=new WeakMap,Le=new WeakMap,Ie=new WeakMap,je={})}},Be=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),We(r,o,n,"aria-hidden")):function(){return null}},$e="Dialog",[Ue,Ke]=function(n,r=[]){let o=[];const i=()=>{const t=o.map(t=>e.createContext(t));return function(r){const o=r?.[n]||t;return e.useMemo(()=>({[`__scope${n}`]:{...r,[n]:o}}),[r,o])}};return i.scopeName=n,[function(r,i){const a=e.createContext(i),c=o.length;o=[...o,i];const u=r=>{const{scope:o,children:i,...u}=r,s=o?.[n]?.[c]||a,l=e.useMemo(()=>u,Object.values(u));return t.jsx(s.Provider,{value:l,children:i})};return u.displayName=r+"Provider",[u,function(t,o){const u=o?.[n]?.[c]||a,s=e.useContext(u);if(s)return s;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},d(i,...r)]}($e),[Xe,Ye]=Ue($e),ze=n=>{const{__scopeDialog:r,children:o,open:i,defaultOpen:a,onOpenChange:c,modal:u=!0}=n,s=e.useRef(null),l=e.useRef(null),[d,f]=g({prop:i,defaultProp:a??!1,onChange:c,caller:$e});return t.jsx(Xe,{scope:r,triggerRef:s,contentRef:l,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:e.useCallback(()=>f(e=>!e),[f]),modal:u,children:o})};ze.displayName=$e;var Ve="DialogTrigger";e.forwardRef((e,n)=>{const{__scopeDialog:r,...o}=e,i=Ye(Ve,r),a=l(n,i.triggerRef);return t.jsx(C.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":pt(i.open),...o,ref:a,onClick:c(e.onClick,i.onOpenToggle)})}).displayName=Ve;var Ze="DialogPortal",[He,qe]=Ue(Ze,{forceMount:void 0}),Ge=n=>{const{__scopeDialog:r,forceMount:o,children:i,container:a}=n,c=Ye(Ze,r);return t.jsx(He,{scope:r,forceMount:o,children:e.Children.map(i,e=>t.jsx(K,{present:o||c.open,children:t.jsx(U,{asChild:!0,container:a,children:e})}))})};Ge.displayName=Ze;var Je="DialogOverlay",Qe=e.forwardRef((e,n)=>{const r=qe(Je,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,a=Ye(Je,e.__scopeDialog);return a.modal?t.jsx(K,{present:o||a.open,children:t.jsx(tt,{...i,ref:n})}):null});Qe.displayName=Je;var et=y("DialogOverlay.RemoveScroll"),tt=e.forwardRef((e,n)=>{const{__scopeDialog:r,...o}=e,i=Ye(Je,r);return t.jsx(Ae,{as:et,allowPinchZoom:!0,shards:[i.contentRef],children:t.jsx(C.div,{"data-state":pt(i.open),...o,ref:n,style:{pointerEvents:"auto",...o.style}})})}),nt="DialogContent",rt=e.forwardRef((e,n)=>{const r=qe(nt,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,a=Ye(nt,e.__scopeDialog);return t.jsx(K,{present:o||a.open,children:a.modal?t.jsx(ot,{...i,ref:n}):t.jsx(it,{...i,ref:n})})});rt.displayName=nt;var ot=e.forwardRef((n,r)=>{const o=Ye(nt,n.__scopeDialog),i=e.useRef(null),a=l(r,o.contentRef,i);return e.useEffect(()=>{const e=i.current;if(e)return Be(e)},[]),t.jsx(at,{...n,ref:a,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:c(n.onCloseAutoFocus,e=>{e.preventDefault(),o.triggerRef.current?.focus()}),onPointerDownOutside:c(n.onPointerDownOutside,e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:c(n.onFocusOutside,e=>e.preventDefault())})}),it=e.forwardRef((n,r)=>{const o=Ye(nt,n.__scopeDialog),i=e.useRef(!1),a=e.useRef(!1);return t.jsx(at,{...n,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{n.onCloseAutoFocus?.(e),e.defaultPrevented||(i.current||o.triggerRef.current?.focus(),e.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:e=>{n.onInteractOutside?.(e),e.defaultPrevented||(i.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const t=e.target,r=o.triggerRef.current?.contains(t);r&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})}),at=e.forwardRef((n,r)=>{const{__scopeDialog:o,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:c,...u}=n,s=Ye(nt,o),d=e.useRef(null),f=l(r,d);return e.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??z()),document.body.insertAdjacentElement("beforeend",e[1]??z()),Y++,()=>{1===Y&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),Y--}},[]),t.jsxs(t.Fragment,{children:[t.jsx(I,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:c,children:t.jsx(P,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":pt(s.open),...u,ref:f,onDismiss:()=>s.onOpenChange(!1)})}),t.jsxs(t.Fragment,{children:[t.jsx(gt,{titleId:s.titleId}),t.jsx(yt,{contentRef:d,descriptionId:s.descriptionId})]})]})}),ct="DialogTitle",ut=e.forwardRef((e,n)=>{const{__scopeDialog:r,...o}=e,i=Ye(ct,r);return t.jsx(C.h2,{id:i.titleId,...o,ref:n})});ut.displayName=ct;var st="DialogDescription",lt=e.forwardRef((e,n)=>{const{__scopeDialog:r,...o}=e,i=Ye(st,r);return t.jsx(C.p,{id:i.descriptionId,...o,ref:n})});lt.displayName=st;var dt="DialogClose",ft=e.forwardRef((e,n)=>{const{__scopeDialog:r,...o}=e,i=Ye(dt,r);return t.jsx(C.button,{type:"button",...o,ref:n,onClick:c(e.onClick,()=>i.onOpenChange(!1))})});function pt(e){return e?"open":"closed"}ft.displayName=dt;var mt="DialogTitleWarning",[vt,ht]=function(n,r){const o=e.createContext(r),i=n=>{const{children:r,...i}=n,a=e.useMemo(()=>i,Object.values(i));return t.jsx(o.Provider,{value:a,children:r})};return i.displayName=n+"Provider",[i,function(t){const i=e.useContext(o);if(i)return i;if(void 0!==r)return r;throw new Error(`\`${t}\` must be used within \`${n}\``)}]}(mt,{contentName:nt,titleName:ct,docsSlug:"dialog"}),gt=({titleId:t})=>{const n=ht(mt),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect(()=>{if(t){document.getElementById(t)}},[r,t]),null},yt=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ht("DialogDescriptionWarning").contentName}}.`;return e.useEffect(()=>{const e=t.current?.getAttribute("aria-describedby");if(n&&e){document.getElementById(n)}},[r,t,n]),null},Et=ze,bt=Ge,wt=Qe,Ct=rt,Nt=ut,Rt=lt,Ot=ft;export{Ct as C,Rt as D,wt as O,bt as P,a as R,Nt as T,Et as a,Ot as b,i as r};
