/**
 * Bhagavad Gita Quotes Collection
 * A curated collection of wisdom from the Bhagavad Gita
 */

export const gitaQuotes = [
  {
    id: 1,
    verse: "2.47",
    sanskrit: "कर्मण्येवाधिकारस्ते मा फलेषु कदाचन। मा कर्मफलहेतुर्भूर्मा ते सङ्गोऽस्त्वकर्मणि॥",
    english: "You have a right to perform your prescribed duty, but not to the fruits of action. Never consider yourself the cause of the results of your activities, and never be attached to not doing your duty.",
    meaning: "Focus on your actions and duties without attachment to the results. This teaches us about selfless service and detachment from outcomes.",
    category: "Karma Yoga",
    tags: ["duty", "detachment", "action", "results"]
  },
  {
    id: 2,
    verse: "2.20",
    sanskrit: "न जायते म्रियते वा कदाचिन्नायं भूत्वा भविता वा न भूयः। अजो नित्यः शाश्वतोऽयं पुराणो न हन्यते हन्यमाने शरीरे॥",
    english: "For the soul there is neither birth nor death. It is not slain when the body is slain.",
    meaning: "The eternal nature of the soul teaches us that we are more than our physical bodies. Death is merely a transition, not an end.",
    category: "Atman",
    tags: ["soul", "eternal", "death", "body", "spiritual"]
  },
  {
    id: 3,
    verse: "6.5",
    sanskrit: "उद्धरेदात्मनात्मानं नात्मानमवसादयेत्। आत्मैव ह्यात्मनो बन्धुरात्मैव रिपुरात्मनः॥",
    english: "One must deliver himself with the help of his mind, and not degrade himself. The mind is the friend of the conditioned soul, and his enemy as well.",
    meaning: "We are responsible for our own spiritual elevation. The mind can be our greatest ally or our worst enemy, depending on how we train it.",
    category: "Self-Realization",
    tags: ["mind", "self-improvement", "responsibility", "spiritual growth"]
  },
  {
    id: 4,
    verse: "18.66",
    sanskrit: "सर्वधर्मान्परित्यज्य मामेकं शरणं व्रज। अहं त्वां सर्वपापेभ्यो मोक्षयिष्यामि मा शुचः॥",
    english: "Abandon all varieties of religion and just surrender unto Me. I shall deliver you from all sinful reactions. Do not fear.",
    meaning: "Complete surrender to the Divine brings liberation from all fears and sufferings. This is the ultimate teaching of devotion and trust.",
    category: "Bhakti Yoga",
    tags: ["surrender", "devotion", "liberation", "fear", "divine"]
  },
  {
    id: 5,
    verse: "2.62-63",
    sanskrit: "ध्यायतो विषयान्पुंसः सङ्गस्तेषूपजायते। सङ्गात्सञ्जायते कामः कामात्क्रोधोऽभिजायते॥",
    english: "While contemplating the objects of the senses, a person develops attachment for them, and from such attachment lust develops, and from lust anger arises.",
    meaning: "This verse explains the chain of mental states that lead to suffering: contemplation → attachment → desire → anger. Understanding this helps us break free from negative patterns.",
    category: "Mind Control",
    tags: ["attachment", "desire", "anger", "contemplation", "suffering"]
  },
  {
    id: 6,
    verse: "4.7-8",
    sanskrit: "यदा यदा हि धर्मस्य ग्लानिर्भवति भारत। अभ्युत्थानमधर्मस्य तदात्मानं सृजाम्यहम्॥",
    english: "Whenever and wherever there is a decline in religious practice and a predominant rise of irreligion, at that time I descend Myself.",
    meaning: "The Divine manifests whenever righteousness declines and evil prevails. This gives hope that good will always triumph over evil.",
    category: "Divine Incarnation",
    tags: ["righteousness", "evil", "divine intervention", "hope", "justice"]
  },
  {
    id: 7,
    verse: "15.7",
    sanskrit: "ममैवांशो जीवलोके जीवभूतः सनातनः। मनःषष्ठानीन्द्रियाणि प्रकृतिस्थानि कर्षति॥",
    english: "The living entities in this conditioned world are My eternal fragmental parts. Due to conditioned life, they are struggling very hard with the six senses, which include the mind.",
    meaning: "All living beings are eternal parts of the Divine. Our struggles in material life are due to our identification with the temporary body and senses rather than our true spiritual nature.",
    category: "Spiritual Identity",
    tags: ["soul", "divine connection", "struggle", "senses", "eternal"]
  },
  {
    id: 8,
    verse: "9.22",
    sanskrit: "अनन्याश्चिन्तयन्तो मां ये जनाः पर्युपासते। तेषां नित्याभियुक्तानां योगक्षेमं वहाम्यहम्॥",
    english: "But those who always worship Me with exclusive devotion, meditating on My transcendental form—to them I carry what they lack, and I preserve what they have.",
    meaning: "Complete devotion and surrender to the Divine ensures that all our needs are taken care of. This teaches us about faith and divine providence.",
    category: "Devotion",
    tags: ["worship", "devotion", "divine care", "faith", "providence"]
  },
  {
    id: 9,
    verse: "2.14",
    sanskrit: "मात्रास्पर्शास्तु कौन्तेय शीतोष्णसुखदुःखदाः। आगमापायिनोऽनित्यास्तांस्तितिक्षस्व भारत॥",
    english: "O son of Kunti, the nonpermanent appearance of happiness and distress, and their disappearance in due course, are like the appearance and disappearance of winter and summer seasons. They arise from sense perception, and one must learn to tolerate them without being disturbed.",
    meaning: "Happiness and sorrow are temporary like seasons. Learning to remain balanced during both good and bad times is essential for spiritual growth.",
    category: "Equanimity",
    tags: ["happiness", "sorrow", "temporary", "balance", "tolerance"]
  },
  {
    id: 10,
    verse: "7.19",
    sanskrit: "बहूनां जन्मनामन्ते ज्ञानवान्मां प्रपद्यते। वासुदेवः सर्वमिति स महात्मा सुदुर्लभः॥",
    english: "After many births and deaths, he who is actually in knowledge surrenders unto Me, knowing Me to be the cause of all causes and all that is. Such a great soul is very rare.",
    meaning: "True spiritual realization comes after many lifetimes of learning. Recognizing the Divine as the source of everything is the highest wisdom and is achieved by very few.",
    category: "Spiritual Wisdom",
    tags: ["knowledge", "surrender", "realization", "rare", "wisdom"]
  }
];

/**
 * Get a random Gita quote
 */
export const getRandomQuote = () => {
  const randomIndex = Math.floor(Math.random() * gitaQuotes.length);
  return gitaQuotes[randomIndex];
};

/**
 * Get quote by ID
 */
export const getQuoteById = (id) => {
  return gitaQuotes.find(quote => quote.id === id);
};

/**
 * Get quotes by category
 */
export const getQuotesByCategory = (category) => {
  return gitaQuotes.filter(quote => 
    quote.category.toLowerCase().includes(category.toLowerCase())
  );
};

/**
 * Get quotes by tag
 */
export const getQuotesByTag = (tag) => {
  return gitaQuotes.filter(quote => 
    quote.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
  );
};

/**
 * Search quotes by text
 */
export const searchQuotes = (searchTerm) => {
  const term = searchTerm.toLowerCase();
  return gitaQuotes.filter(quote => 
    quote.english.toLowerCase().includes(term) ||
    quote.meaning.toLowerCase().includes(term) ||
    quote.category.toLowerCase().includes(term) ||
    quote.tags.some(tag => tag.toLowerCase().includes(term))
  );
};
