# Environment Configuration
NODE_ENV=development
PORT=3000

# API Keys (Required)
OPENAI_API_KEY=your_openai_api_key_here
ELEVEN_LABS_API_KEY=your_elevenlabs_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI Configuration
OPENAI_MODEL=gpt-3.5-turbo-1106
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.6
OPENAI_TIMEOUT=15000

# Text-to-Speech Configuration
TTS_MODEL=gpt-4o-mini-tts
TTS_VOICE=coral
TTS_SPEED=1.0
TTS_INSTRUCTIONS=Speak in a warm, friendly, and spiritual tone as <PERSON>, the divine guide from the Bhagavad Gita.
TTS_RESPONSE_FORMAT=mp3

# ElevenLabs Configuration
ELEVEN_LABS_VOICE_ID=pNInz6obpgDQGcFmaJgB
ELEVEN_LABS_MODEL=eleven_monolingual_v1
ELEVEN_LABS_STABILITY=0.5
ELEVEN_LABS_SIMILARITY_BOOST=0.5
ELEVEN_LABS_STYLE=0.0
ELEVEN_LABS_SPEAKER_BOOST=true

# Gemini Configuration
GEMINI_MODEL=gemini-2.0-flash
GEMINI_TTS_MODEL=gemini-2.5-flash-preview-tts
GEMINI_MAX_TOKENS=1000
GEMINI_TEMPERATURE=0.7
GEMINI_TIMEOUT=20000
GEMINI_TTS_VOICE=Kore

# Audio Processing
MAX_AUDIO_FILES=10
RHUBARB_PATH=./bin/rhubarb.exe
FFMPEG_TIMEOUT=30000

# Logging
LOG_LEVEL=info
MAX_LOGS=5
LOG_FORMAT=combined

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=*
REQUEST_SIZE_LIMIT=10mb

# Session Management
SESSION_TIMEOUT_MS=1800000
SESSION_WARNING_MS=300000
JWT_EXPIRATION_MS=3600000

# Performance
CLEANUP_INTERVAL=3600000

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Clerk Authentication
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
CLERK_WEBHOOK_SECRET=your_clerk_webhook_secret

# Load Balancer Configuration
LOAD_BALANCER_ENABLED=false
BACKEND_INSTANCES=1

# Database Logging Configuration
LOG_TO_DATABASE=true
LOG_RETENTION_DAYS=30

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Email Configuration (Gmail - Always Used)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM=GitaGPT <<EMAIL>>
EMAIL_SERVICE=gmail

# Custom SMTP Configuration (alternative to EMAIL_SERVICE)
# EMAIL_SERVICE=smtp
# EMAIL_HOST=smtp.yourdomain.com
# EMAIL_PORT=587
# EMAIL_SECURE=false
