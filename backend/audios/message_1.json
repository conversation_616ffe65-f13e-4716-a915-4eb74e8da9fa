{"metadata": {"soundFile": "D:\\gitaGPT\\backend\\audios\\message_1.wav", "duration": 10.15}, "mouthCues": [{"start": 0.0, "end": 0.02, "value": "X"}, {"start": 0.02, "end": 0.13, "value": "B"}, {"start": 0.13, "end": 0.21, "value": "A"}, {"start": 0.21, "end": 0.37, "value": "D"}, {"start": 0.37, "end": 0.44, "value": "C"}, {"start": 0.44, "end": 0.52, "value": "A"}, {"start": 0.52, "end": 0.61, "value": "C"}, {"start": 0.61, "end": 0.96, "value": "B"}, {"start": 0.96, "end": 1.03, "value": "C"}, {"start": 1.03, "end": 1.24, "value": "B"}, {"start": 1.24, "end": 1.37, "value": "X"}, {"start": 1.37, "end": 1.43, "value": "B"}, {"start": 1.43, "end": 1.48, "value": "E"}, {"start": 1.48, "end": 2.04, "value": "B"}, {"start": 2.04, "end": 2.25, "value": "C"}, {"start": 2.25, "end": 2.81, "value": "B"}, {"start": 2.81, "end": 2.88, "value": "E"}, {"start": 2.88, "end": 2.95, "value": "F"}, {"start": 2.95, "end": 3.02, "value": "C"}, {"start": 3.02, "end": 3.1, "value": "A"}, {"start": 3.1, "end": 3.25, "value": "B"}, {"start": 3.25, "end": 3.39, "value": "C"}, {"start": 3.39, "end": 3.46, "value": "B"}, {"start": 3.46, "end": 3.6, "value": "D"}, {"start": 3.6, "end": 3.67, "value": "C"}, {"start": 3.67, "end": 3.75, "value": "A"}, {"start": 3.75, "end": 3.9, "value": "B"}, {"start": 3.9, "end": 3.98, "value": "A"}, {"start": 3.98, "end": 4.09, "value": "B"}, {"start": 4.09, "end": 4.3, "value": "C"}, {"start": 4.3, "end": 4.58, "value": "B"}, {"start": 4.58, "end": 4.72, "value": "C"}, {"start": 4.72, "end": 5.3, "value": "B"}, {"start": 5.3, "end": 5.37, "value": "C"}, {"start": 5.37, "end": 5.44, "value": "E"}, {"start": 5.44, "end": 5.51, "value": "G"}, {"start": 5.51, "end": 5.72, "value": "B"}, {"start": 5.72, "end": 5.86, "value": "C"}, {"start": 5.86, "end": 6.14, "value": "B"}, {"start": 6.14, "end": 6.35, "value": "H"}, {"start": 6.35, "end": 6.42, "value": "C"}, {"start": 6.42, "end": 6.63, "value": "B"}, {"start": 6.63, "end": 6.84, "value": "D"}, {"start": 6.84, "end": 6.91, "value": "B"}, {"start": 6.91, "end": 6.99, "value": "A"}, {"start": 6.99, "end": 7.22, "value": "B"}, {"start": 7.22, "end": 7.29, "value": "F"}, {"start": 7.29, "end": 7.43, "value": "B"}, {"start": 7.43, "end": 7.5, "value": "F"}, {"start": 7.5, "end": 7.71, "value": "B"}, {"start": 7.71, "end": 7.92, "value": "D"}, {"start": 7.92, "end": 7.99, "value": "B"}, {"start": 7.99, "end": 8.07, "value": "A"}, {"start": 8.07, "end": 8.14, "value": "D"}, {"start": 8.14, "end": 8.46, "value": "B"}, {"start": 8.46, "end": 8.53, "value": "F"}, {"start": 8.53, "end": 8.88, "value": "B"}, {"start": 8.88, "end": 10.15, "value": "X"}]}