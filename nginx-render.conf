# Optimize worker processes
worker_processes auto;
worker_rlimit_nofile 65535;

# Error logging
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Events block optimization
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format with performance metrics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    client_max_body_size 10M;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Buffer optimizations
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # Security headers (global)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # Direct proxy to backend (no upstream needed for single server)

    # Main server block
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Connection limiting
        limit_conn conn_limit_per_ip 20;

        # Security headers (server-specific) - Single CSP header
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://gitagpt-backend-************.asia-south2.run.app wss://gitagpt-backend-************.asia-south2.run.app https://clerk.com https://*.clerk.accounts.dev;" always;
        add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;

        # Hide nginx version
        server_tokens off;

        # Health check endpoint
        location = /health {
            access_log off;
            return 200 "healthy - backend: gitagpt-backend-************.asia-south2.run.app - build: **********\n";
            add_header Content-Type text/plain;
        }

        # Backend ping endpoint proxy
        location = /ping {
            proxy_pass https://gitagpt-backend-************.asia-south2.run.app/ping;
            proxy_http_version 1.1;
            proxy_set_header Host gitagpt-backend-************.asia-south2.run.app;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;

            # CORS headers
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;

            # Strip all Cloudflare cookies
            proxy_hide_header Set-Cookie;
            proxy_set_header Cookie "";
        }

        # API proxy with rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            limit_req zone=general burst=50 nodelay;

            # Proxy to backend with /api prefix intact
            proxy_pass https://gitagpt-backend-************.asia-south2.run.app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host gitagpt-backend-************.asia-south2.run.app;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;

            # Error handling
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 2;
            proxy_next_upstream_timeout 30s;

            # CORS headers for API calls
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;

            # Strip problematic Cloudflare cookies
            proxy_hide_header Set-Cookie;
            proxy_set_header Cookie "";

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin * always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
                return 204;
            }
        }

        # Auth endpoints with stricter rate limiting
        location ~ ^/api/v1/auth/ {
            limit_req zone=auth burst=5 nodelay;

            proxy_pass https://gitagpt-backend-************.asia-south2.run.app;
            proxy_http_version 1.1;
            proxy_set_header Host gitagpt-backend-************.asia-south2.run.app;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Timeouts for auth
            proxy_connect_timeout 15s;
            proxy_send_timeout 15s;
            proxy_read_timeout 15s;

            # CORS headers for auth
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;

            # Strip all Cloudflare cookies
            proxy_hide_header Set-Cookie;
            proxy_set_header Cookie "";

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin * always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
                return 204;
            }
        }

        # WebSocket support
        location /ws {
            proxy_pass https://gitagpt-backend-************.asia-south2.run.app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host gitagpt-backend-************.asia-south2.run.app;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Strip all Cloudflare cookies
            proxy_hide_header Set-Cookie;
            proxy_set_header Cookie "";

            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # Static assets with aggressive caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            
            # Compression for static files
            gzip_static on;
            
            # Security for static files
            add_header X-Content-Type-Options nosniff;
            
            try_files $uri =404;
        }

        # HTML files with shorter cache
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header Vary Accept-Encoding;
        }

        # Frontend routes (SPA support)
        location / {
            limit_req zone=general burst=50 nodelay;
            
            try_files $uri $uri/ @fallback;
            
            # Security for HTML
            add_header X-Frame-Options "SAMEORIGIN";
            add_header X-XSS-Protection "1; mode=block";
        }

        # Fallback for client-side routing
        location @fallback {
            rewrite ^.*$ /index.html last;
        }

        # Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Error pages
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
