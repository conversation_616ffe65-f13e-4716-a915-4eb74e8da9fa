name: Deploy GitaGPT

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: docker.io
  BACKEND_IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/gitagpt-backend
  FRONTEND_IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/gitagpt-frontend

jobs:
  # Test job
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Run backend tests
      run: |
        cd backend
        npm test

    - name: Run frontend linting
      run: |
        cd frontend
        npm run lint

    - name: Build frontend
      run: |
        cd frontend
        npm run build

  # Build and push Docker images
  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    
    strategy:
      matrix:
        service: [backend, frontend]
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ secrets.DOCKERHUB_USERNAME }}/gitagpt-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.service }}
        file: ./${{ matrix.service }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Deploy to staging
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # This could be deploying to a staging server, updating Kubernetes, etc.

  # Deploy to production
  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production'
    environment: production
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Deploy to Render
      run: |
        echo "Deploying to production..."
        # Trigger Render deployment
        curl -X POST "${{ secrets.RENDER_DEPLOY_HOOK_BACKEND }}"
        curl -X POST "${{ secrets.RENDER_DEPLOY_HOOK_FRONTEND }}"

    - name: Health check
      run: |
        echo "Performing health checks..."
        sleep 60  # Wait for deployment
        
        # Check backend health
        curl -f https://gitagpt-backend-latest.onrender.com/ping || exit 1
        
        # Check frontend health  
        curl -f https://your-frontend-url.onrender.com/health || exit 1

    - name: Notify deployment success
      if: success()
      run: |
        echo "✅ Production deployment successful!"
        # Add notification logic (Slack, Discord, email, etc.)

    - name: Notify deployment failure
      if: failure()
      run: |
        echo "❌ Production deployment failed!"
        # Add failure notification logic
